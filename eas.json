{"cli": {"version": ">= 5.0.0", "requireCommit": true, "appVersionSource": "remote", "promptToConfigurePushNotifications": false}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "ios": {"simulator": true}, "android": {"buildType": "apk"}, "env": {"EXPO_PUBLIC_AI_PROVIDER": "openrouter", "EXPO_PUBLIC_OPENROUTER_MODEL": "deepseek/deepseek-chat-v3-0324:free"}}, "preview": {"distribution": "internal", "channel": "preview", "android": {"buildType": "apk"}, "env": {"EXPO_PUBLIC_AI_PROVIDER": "openrouter", "EXPO_PUBLIC_OPENROUTER_MODEL": "deepseek/deepseek-chat-v3-0324:free"}}, "production": {"autoIncrement": true, "channel": "production", "distribution": "store", "ios": {"autoIncrement": "buildNumber"}, "android": {"autoIncrement": "versionCode", "buildType": "app-bundle"}, "env": {"EXPO_PUBLIC_AI_PROVIDER": "openrouter", "EXPO_PUBLIC_OPENROUTER_MODEL": "deepseek/deepseek-chat-v3-0324:free", "EXPO_PUBLIC_SUPABASE_URL": "https://tqnzwbergnfosjkfqyfx.supabase.co", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.OZLyCWZgrNsG8jjeoav5a9DuhD4BcHkuT-9d9exAYeU", "EXPO_PUBLIC_OPENROUTER_API_KEY": "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", "EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY": "u8JHk2k3j9L5s8n2k1j3l5m7o9p0q2r4"}}, "debug-android": {"extends": "production", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease --stacktrace --info"}, "channel": "debug-android"}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>"}, "android": {"track": "production"}}}}