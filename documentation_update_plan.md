# Plan to Update `docs/message_encryption.md`

### **1. Overview Section**

*   Replace the mention of "AES symmetric encryption" with "custom XOR-based obfuscation."
*   Remove the reference to the `crypto-js` library.
*   Update the environment variable to `EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY`.

### **2. Decryption Section**

*   Remove the existing "How to Decrypt Messages in an Emergency" section.
*   Add a new section titled "How to Decrypt Messages in Supabase."
*   Explain how to use the `decrypt_message` SQL function in the Supabase SQL editor.
*   Provide a clear example query.

### **3. Code Examples**

*   Remove the `Node.js` and online tool examples, as they are no longer relevant.
*   Add a SQL example for using the new decryption function.