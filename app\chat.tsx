import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Pressable,
  Animated,
  Dimensions,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useAuth } from '../src/contexts/AuthContext';
import { useTheme } from '../src/contexts/ThemeContext';
import useChat from '../src/hooks/useChat';
import { ChatSidebar } from '../src/components/ChatSidebar';
import { COLORS } from '../src/lib/constants';
import { Message } from '../src/hooks/useMessageManagement';
import { getLocalImagePath } from '../src/lib/imageUtils';
import companionProfiles from '../src/lib/companionProfiles';

// Import extracted components
import MessageItem from '../src/components/MessageItem';
import ChatHeader from '../src/components/ChatHeader';
import ChatInput from '../src/components/ChatInput';
import TypingIndicator from '../src/components/TypingIndicator';

// Helper Types
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
type ExtendedMessage = Message & { is_companion?: boolean };

export default function ChatScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { user } = useAuth();
  const { colors, isDark, toggleTheme } = useTheme();
  const inputRef = useRef<any>(null);
  const flatListRef = useRef<FlatList>(null);

  // Get companion info from params
  const companionId = params.companionId as string;
  const companionName = params.companionName as string;
  const companionImageUrl = params.companionImageUrl as string;

  // Get screen dimensions
  const screenWidth = Dimensions.get('window').width;

  // Sidebar animation
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const slideAnim = useRef(new Animated.Value(-screenWidth * 0.8)).current;

  // Chat state
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isAiTyping, setIsAiTyping] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  // Get user profile image - use app icon as fallback
  const userImageSource = require('../assets/icon.png');

  // For companion image, use local image from assets based on companion ID
  const companionImageSource = useMemo(() => {
    console.log('Companion ID:', companionId);
    console.log('Companion Image URL:', companionImageUrl);

    // First check if we have this companion in our profiles
    if (companionId && companionProfiles[companionId]) {
      // console.log('Found companion in profiles:', companionId);
      return companionProfiles[companionId].localImage;
    }

    // If not in profiles, try to get a local image by ID
    if (companionId) {
      // console.log('Getting local image by ID:', companionId);
      const localImage = getLocalImagePath(companionId);
      // console.log('Local image found:', !!localImage);
      return localImage;
    }

    // If we have a URL, use it (but this is less preferred)
    if (companionImageUrl) {
      // console.log('Using companion image URL');
      return { uri: companionImageUrl };
    }

    // Fallback to app icon
    // console.log('Using fallback app icon');
    return require('../assets/icon.png');
  }, [companionId, companionImageUrl]);

  // Initialize chat hook
  const {
    messages,
    sessions,
    currentSessionId,
    isLoading,
    isInitializing,
    isAiTyping: aiTypingState,
    sendMessage,
    refreshSessions,
    handleNewChat: startNewSession,
    handleSessionSelect,
  } = useChat(companionId);

  // Sync the AI typing state from the hook
  useEffect(() => {
    setIsAiTyping(aiTypingState);
  }, [aiTypingState]);

  // Keyboard height handling
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (event) => {
        // Only set keyboard height on iOS, let Android handle it automatically
        if (Platform.OS === 'ios') {
          setKeyboardHeight(event.endCoordinates.height);
        }
      }
    );
    
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        if (Platform.OS === 'ios') {
          setKeyboardHeight(0);
        }
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Toggle sidebar
  const toggleSidebar = useCallback(() => {
    // console.log('toggleSidebar called, current state:', isSidebarOpen);

    if (isSidebarOpen) {
      // Close sidebar
      // console.log('Closing sidebar');
      Animated.timing(slideAnim, {
        toValue: -screenWidth * 0.8,
        duration: 300,
        useNativeDriver: Platform.OS !== 'web', // Only use native driver on native platforms
      }).start(() => {
        // console.log('Animation completed, setting isSidebarOpen to false');
        setIsSidebarOpen(false);
      });
    } else {
      // Open sidebar
      // console.log('Opening sidebar');
      setIsSidebarOpen(true);
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: Platform.OS !== 'web', // Only use native driver on native platforms
      }).start(() => {
        // console.log('Open animation completed');
      });
    }
  }, [isSidebarOpen, slideAnim, screenWidth]);

  // Handle sending a message
  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim() || isSending || isInitializing) return;

    // Store the message content before clearing input
    const messageContent = newMessage;
    // Clear the input immediately for better UX
    setNewMessage('');

    try {
      setIsSending(true);
      // Call the sendMessage function from useChat with the message content
      await sendMessage(messageContent);
      // AI typing is handled by the useChat hook through isAiTyping
    } catch (error) {
      console.error('Error sending message:', error);
      // Restore the message if sending failed
      setNewMessage(messageContent);
    } finally {
      setIsSending(false);
    }
  }, [newMessage, isSending, isInitializing, sendMessage]);

  // Handle session selection from sidebar
  const onSessionSelect = useCallback(async (sessionId: string) => {
    try {
      // Call the function to switch to the selected session
      await handleSessionSelect(sessionId);

      // Close the sidebar after selection
      toggleSidebar();
    } catch (error) {
      console.error('Error selecting session:', error);
    }
  }, [toggleSidebar, handleSessionSelect]);

  // Handle creating a new chat
  const handleNewChat = useCallback(async () => {
    try {
      await startNewSession();
      toggleSidebar();
    } catch (error) {
      console.error('Error starting new session:', error);
    }
  }, [startNewSession, toggleSidebar]);

  // Render message item
  const renderMessageItem = useCallback(({ item }: { item: ExtendedMessage }) => {
    return (
      <MessageItem
        item={item}
        userImageSource={userImageSource}
        companionImageSource={companionImageSource}
        colors={colors}
      />
    );
  }, [colors, userImageSource, companionImageSource]);

  // Track if user has manually scrolled up
  const [isUserScrolled, setIsUserScrolled] = useState(false);
  const lastContentHeight = useRef(0);
  const isAutoScrolling = useRef(false);

  // Enhanced scroll to bottom function
  const scrollToBottom = useCallback((animated = true) => {
    if (flatListRef.current && (!isUserScrolled || isAiTyping)) {
      isAutoScrolling.current = true;
      flatListRef.current.scrollToEnd({ animated });
      // Reset after animation completes
      setTimeout(() => {
        isAutoScrolling.current = false;
      }, animated ? 300 : 0);
    }
  }, [isUserScrolled, isAiTyping]);

  // Scroll to bottom when new messages arrive or when AI is typing
  useEffect(() => {
    if (messages.length > 0 || isAiTyping) {
      // Immediate scroll without animation for better UX during streaming
      scrollToBottom(false);
    }
  }, [messages, isAiTyping, scrollToBottom]);

  // Handle navigation to auth screen if user is not logged in
  useEffect(() => {
    if (!user) {
      // Use a timeout to ensure navigation happens after component is mounted
      const timer = setTimeout(() => {
        // Redirect to auth with companion info so they can continue as guest
        router.replace({
          pathname: '/auth',
          params: {
            from: 'companion',
            companionId: companionId,
            companionName: companionName
          }
        });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [user, router, companionId, companionName]);

  // Define a memoized value to check if user is logged in
  const isLoggedIn = useMemo(() => !!user, [user]);

  // Get the keyboard vertical offset for iOS - reduced to minimize gap
  const keyboardVerticalOffset = Platform.OS === 'ios' ? 48 : 0;
  const behavior = Platform.OS === 'ios' ? 'padding' : undefined;

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={behavior}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      {!isLoggedIn ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.accent} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Please log in to continue...
          </Text>
        </View>
      ) : (
        <>
          {/* Header with Menu Toggle and Theme Toggle */}
          <ChatHeader
            toggleSidebar={toggleSidebar}
            toggleTheme={toggleTheme}
            isDark={isDark}
            colors={colors}
            companionImageSource={companionImageSource}
          />

          {/* Main Chat Area */}
          <View style={styles.chatAreaContainer}>
            <View 
              style={[
                styles.chatContent,
                { 
                  backgroundColor: colors.background,
                  flex: 1,
                }
              ]}
            >
            {isInitializing ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.accent} />
                <Text style={[styles.loadingText, { color: colors.textSecondary, marginTop: 10 }]}>
                  Initializing chat session...
                </Text>
              </View>
            ) : isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.accent} />
              </View>
            ) : messages.length === 0 && !isLoading ? (
              <View style={styles.emptyChatContainer}>
                <Text style={[styles.emptyChatText, { color: colors.textSecondary }]}>
                  What's on your mind?
                </Text>
              </View>
            ) : (
              <FlatList
                ref={flatListRef}
                data={messages as ExtendedMessage[]}
                renderItem={renderMessageItem}
                keyExtractor={(item: ExtendedMessage) => item.id.toString()}
                contentContainerStyle={styles.messagesList}
                removeClippedSubviews={false}
                maxToRenderPerBatch={10}
                windowSize={21}
                initialNumToRender={15}
                maintainVisibleContentPosition={{
                  minIndexForVisible: 0
                }}
                onScroll={(event) => {
                  if (isAutoScrolling.current) return;

                  const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
                  const paddingToBottom = 20;
                  const isCloseToBottom = layoutMeasurement.height + contentOffset.y >=
                    contentSize.height - paddingToBottom;

                  // Update user scroll state
                  setIsUserScrolled(!isCloseToBottom);
                }}
                onContentSizeChange={(width, height) => {
                  // Only auto-scroll if content height has increased
                  if (height > lastContentHeight.current) {
                    scrollToBottom(true);
                  }
                  lastContentHeight.current = height;
                }}
                onLayout={() => scrollToBottom(false)}
                keyboardShouldPersistTaps="handled"
                keyboardDismissMode="none"
                scrollEventThrottle={16} // For smooth scroll detection
              />
            )}

            {/* AI Typing Indicator */}
            <TypingIndicator isVisible={isAiTyping} colors={colors} />
            </View>
          </View>

          {/* Fixed Input Area at bottom */}
          <View style={{
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderTopColor: colors.cardBorder,
          }}>
            <ChatInput
              ref={inputRef}
              newMessage={newMessage}
              setNewMessage={setNewMessage}
              handleSendMessage={handleSendMessage}
              isSending={isSending}
              isInitializing={isInitializing}
              colors={colors}
            />
          </View>

          {/* Sidebar - No longer using Modal */}
          {isSidebarOpen && (
            <View
              style={[styles.overlay, {
                backgroundColor: isDark ? 'rgba(0, 0, 0, 0.6)' : 'rgba(0, 0, 0, 0.4)',
                position: 'absolute',
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                zIndex: 1000,
              }]}
            >
              <Pressable
                style={styles.overlayPressable}
                onPress={toggleSidebar}
              />
              <Animated.View
                style={[
                  styles.sidebarContainer,
                  {
                    transform: [{ translateX: slideAnim }],
                    backgroundColor: colors.card || colors.background
                  }
                ]}
              >
                <ChatSidebar
                  sessions={sessions}
                  currentSessionId={currentSessionId}
                  onSessionSelect={onSessionSelect}
                  onClose={toggleSidebar}
                  onRefresh={refreshSessions}
                  onNewChat={handleNewChat} // Pass the handleNewChat function
                />
              </Animated.View>
            </View>
          )}
        </>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  chatAreaContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  chatContent: {
    flex: 1,
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
  },
  emptyChatContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyChatText: {
    fontSize: 16,
    color: COLORS.gray[500],
    textAlign: 'center',
  },
  messagesList: {
    paddingVertical: 16,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  overlayPressable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  sidebarContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    zIndex: 1001, // Higher than the overlay
  },
});