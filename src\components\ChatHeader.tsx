import React, { memo } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface ChatHeaderProps {
  toggleSidebar: () => void;
  toggleTheme: () => void;
  isDark: boolean;
  colors: any;
  companionImageSource: any;
}

/**
 * ChatHeader component displays the header of the chat screen
 * Includes menu toggle, back button, theme toggle, and companion image
 */
const ChatHeader = memo(({
  toggleSidebar,
  toggleTheme,
  isDark,
  colors,
  companionImageSource
}: ChatHeaderProps) => {
  const router = useRouter();

  return (
    <View style={[styles.header, { backgroundColor: colors.surface }]}>
      {/* Left Side: Menu Toggle and Back Button */}
      <View style={styles.headerLeft}>
        <TouchableOpacity
          style={styles.headerIconContainer}
          onPress={toggleSidebar}
        >
          <Ionicons name="menu-outline" size={28} color={colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.headerIconContainer}
          onPress={() => router.back()}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons name="arrow-back" size={24} color={colors.textSecondary} />
            <Text style={{ color: colors.textSecondary, marginLeft: 5 }}>Back to Companions</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Center/Right: Companion Image and Theme Toggle */}
      <View style={styles.headerRight}>
        <TouchableOpacity
          style={styles.themeToggleButton}
          onPress={toggleTheme}
        >
          <Ionicons
            name={isDark ? "sunny-outline" : "moon-outline"}
            size={24}
            color={colors.textSecondary}
          />
        </TouchableOpacity>
        <Image
          source={companionImageSource || require('../../assets/icon.png')}
          style={styles.headerProfilePic}
        />
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIconContainer: {
    padding: 6,
    marginRight: 10,
  },
  themeToggleButton: {
    padding: 6,
    marginRight: 10,
    borderRadius: 20,
  },
  headerProfilePic: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
});

export default ChatHeader;
