/**
 * Active Settings Screen
 *
 * This is the main settings screen used by the app through Expo Router.
 * Any changes to the settings UI should be made here, not in src/screens/SettingsScreen.tsx
 * which is deprecated.
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
  Modal,
  TextInput,
  Image,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../src/contexts/AuthContext';
import { supabase } from '../src/lib/supabase';
import { useTheme } from '../src/contexts/ThemeContext';
import { useUserConsent } from '../src/contexts/UserConsentContext';
import { encryptedStorageAdapter } from '../src/lib/encryptedStorageAdapter';
import { Ionicons } from '@expo/vector-icons';
import * as Updates from 'expo-updates';

import { Footer } from '../src/components/ui/Footer';

export default function SettingsScreen() {
  const router = useRouter();
  const { user, signOut } = useAuth();
  const { toggleTheme, colors, isDark } = useTheme();
  const { setShowConsentDialog } = useUserConsent();

  const [bugReportVisible, setBugReportVisible] = useState(false);
  const [bugDescription, setBugDescription] = useState('');
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [updateStatus, setUpdateStatus] = useState('');
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [isCheckingForUpdate, setIsCheckingForUpdate] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace('/auth');
    } catch {
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
  };

  const handleDeleteAccount = async () => {
    if (!user) return;

    try {
      // Delete user data from Supabase
      const { error } = await supabase.rpc('delete_user_account');

      if (error) throw error;

      // Sign out after account deletion
      await signOut();
      router.replace('/auth');

      Alert.alert(
        'Account Deleted',
        'Your account and all associated data have been permanently deleted.'
      );
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to delete account. Please try again later.'
      );
      console.error('Error deleting account:', error);
    }
  };

  const handleBugReport = async () => {
    if (!bugDescription) {
      Alert.alert('Error', 'Please provide a description for your bug report.');
      return;
    }

    try {
      const { error } = await supabase
        .from('bug_reports')
        .insert({
          user_id: user?.id,
          description: bugDescription,
          app_version: '1.0.0', // Get this from app config in a real app
          device_info: Platform.OS + ' ' + Platform.Version,
        });

      if (error) throw error;

      Alert.alert(
        'Bug Report Submitted',
        'Thank you for your feedback! We will investigate this issue.'
      );

      // Reset form and close modal
      setBugDescription('');
      setBugReportVisible(false);
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to submit bug report. Please try again later.'
      );
      console.error('Error submitting bug report:', error);
    }
  };

  const resetPrivacyPreferences = async () => {
    Alert.alert(
      'Reset Privacy Preferences',
      'This will show the privacy consent dialog again. You can update your privacy choices there. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          onPress: async () => {
            try {
              // Reset the consent status in storage
              await encryptedStorageAdapter.setItem('user_consent_given', 'false');

              // Show the consent dialog again
              setShowConsentDialog(true);

              Alert.alert(
                'Success',
                'Privacy preferences reset. The consent dialog will appear now.'
              );
            } catch (error) {
              console.error('Error resetting privacy preferences:', error);
              Alert.alert('Error', 'Failed to reset privacy preferences. Please try again.');
            }
          },
          style: 'default'
        }
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push('/')}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
          <Text style={[styles.backButtonText, { color: colors.text }]}>Back</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Settings</Text>
        {/* Empty view for centering */}
        <View style={{ width: 32 }} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}>
        {/* Account Section */}
        <View style={[styles.section, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Account</Text>

          <View style={styles.accountInfo}>
            <View style={[styles.avatarPlaceholder, { backgroundColor: colors.accent }]}>
              <Text style={styles.avatarText}>
                {user?.email?.charAt(0).toUpperCase() || '?'}
              </Text>
            </View>
            <View style={styles.accountDetails}>
              <Text style={[styles.emailText, { color: colors.text }]}>
                {user?.email || 'Not signed in'}
              </Text>
              {user?.user_metadata?.name && (
                <Text style={[styles.nameText, { color: colors.textSecondary }]}>
                  {user.user_metadata.name}
                </Text>
              )}
            </View>
          </View>

          {user ? (
            <TouchableOpacity
              style={[styles.settingItem, { borderBottomColor: colors.border }]}
              onPress={handleSignOut}
            >
              <Ionicons name="log-out-outline" size={22} color={colors.danger} />
              <Text style={[styles.settingText, { color: colors.danger }]}>Sign Out</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.settingItem, { borderBottomColor: colors.border }]}
              onPress={() => router.push('/auth')}
            >
              <Ionicons name="log-in-outline" size={22} color={colors.primary} />
              <Text style={[styles.settingText, { color: colors.primary }]}>Sign In</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Appearance Section */}
        <View style={[styles.section, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Appearance</Text>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={toggleTheme}
          >
            <Ionicons
              name={isDark ? "sunny-outline" : "moon-outline"}
              size={22}
              color={colors.text}
            />
            <Text style={[styles.settingText, { color: colors.text }]}>
              {isDark ? "Light Mode" : "Dark Mode"}
            </Text>
            <View style={[styles.themeIndicator, { backgroundColor: colors.accent }]}>
              <Text style={styles.themeIndicatorText}>
                {isDark ? "Dark" : "Light"}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Support Section */}
        <View style={[styles.section, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Support</Text>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={async () => {
              try {
                setIsCheckingForUpdate(true);
                setUpdateStatus('Checking for updates...');
                
                const update = await Updates.checkForUpdateAsync();
                
                if (update.isAvailable) {
                  setIsUpdateAvailable(true);
                  setUpdateStatus('Update available! Downloading...');
                  
                  // Download the update
                  await Updates.fetchUpdateAsync();
                  
                  // Notify the user to restart the app
                  setUpdateStatus('Update downloaded! Restart the app to apply.');
                  Alert.alert(
                    'Update Ready',
                    'A new version has been downloaded. Would you like to restart the app now?',
                    [
                      {
                        text: 'Later',
                        style: 'cancel',
                      },
                      {
                        text: 'Restart Now',
                        onPress: () => Updates.reloadAsync(),
                      },
                    ]
                  );
                } else {
                  setUpdateStatus('You are using the latest version');
                  setTimeout(() => setUpdateStatus(''), 3000); // Clear status after 3 seconds
                }
              } catch (error) {
                console.error('Error checking for updates:', error);
                setUpdateStatus('Failed to check for updates');
                setTimeout(() => setUpdateStatus(''), 3000); // Clear status after 3 seconds
              } finally {
                setIsCheckingForUpdate(false);
              }
            }}
            disabled={isCheckingForUpdate}
          >
            <Ionicons name="refresh-outline" size={22} color={colors.text} />
            <Text style={[styles.settingText, { color: colors.text }]}>
              {isCheckingForUpdate ? 'Checking...' : 'Check for Updates'}
            </Text>
            {updateStatus ? (
              <Text 
                style={[
                  styles.updateStatusText, 
                  { 
                    color: updateStatus.includes('latest') ? colors.success : colors.textSecondary,
                    marginLeft: 'auto',
                    marginRight: 8,
                    fontSize: 12,
                  }
                ]}
              >
                {updateStatus}
              </Text>
            ) : null}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => setBugReportVisible(true)}
          >
            <Ionicons name="bug-outline" size={22} color={colors.text} />
            <Text style={[styles.settingText, { color: colors.text }]}>Report a Bug</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => Linking.openURL('https://papayawhip-flamingo-501988.hostingersite.com/privacy.html')}
          >
            <Ionicons name="document-text-outline" size={22} color={colors.text} />
            <Text style={[styles.settingText, { color: colors.text }]}>Privacy Policy</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => Linking.openURL('https://papayawhip-flamingo-501988.hostingersite.com/terms.html')}
          >
            <Ionicons name="document-outline" size={22} color={colors.text} />
            <Text style={[styles.settingText, { color: colors.text }]}>Terms of Service</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => Linking.openURL('mailto:<EMAIL>')}
          >
            <Ionicons name="mail-outline" size={22} color={colors.text} />
            <Text style={[styles.settingText, { color: colors.text }]}>Contact Support</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={resetPrivacyPreferences}
          >
            <Ionicons name="refresh-outline" size={22} color={colors.text} />
            <Text style={[styles.settingText, { color: colors.text }]}>Reset Privacy Preferences</Text>
          </TouchableOpacity>
        </View>

        {/* Danger Zone */}
        <View style={[styles.section, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.danger }]}>Danger Zone</Text>

          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => Linking.openURL('https://papayawhip-flamingo-501988.hostingersite.com/delete-account.html')}
          >
            <Ionicons name="information-circle-outline" size={22} color={colors.danger} />
            <Text style={[styles.settingText, { color: colors.danger }]}>Account Deletion Info</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setDeleteConfirmVisible(true)}
          >
            <Ionicons name="trash-outline" size={22} color={colors.danger} />
            <Text style={[styles.settingText, { color: colors.danger }]}>Delete Account</Text>
          </TouchableOpacity>
        </View>

        {/* Disclaimer */}
        <View style={[styles.section, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.accent }]}>AI Disclaimer & Emergency Notice</Text>

          <View style={[styles.disclaimerContent, { backgroundColor: isDark ? 'rgba(239,68,68,0.1)' : 'rgba(255,241,242,0.9)' }]}>
            <Text style={[styles.disclaimerText, { color: colors.text }]}>
              This app uses artificial intelligence to generate responses for informational and educational purposes only. It is not intended to provide medical advice, diagnosis, or treatment. The information provided may be inaccurate or incomplete and should not be used as a substitute for professional care. By using this app, you agree that you are solely responsible for your personal decisions. If you are experiencing a crisis or need immediate assistance, please contact a licensed professional, call emergency services, or reach out to your local crisis hotline. Always consult with appropriate professionals for any health-related concerns.
            </Text>
          </View>
        </View>

        {/* Spacer to push footer to bottom */}
        <View style={{ flex: 1 }} />

        {/* Footer */}
        <Footer textColor={colors.text} backgroundColor={colors.card} />
      </ScrollView>

      {/* Bug Report Modal */}
      <Modal
        visible={bugReportVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setBugReportVisible(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.modalContainer}
        >
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>Report a Bug</Text>
              <TouchableOpacity
                onPress={() => setBugReportVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <TextInput
              style={[
                styles.input,
                styles.textArea,
                { backgroundColor: colors.inputBackground, color: colors.text }
              ]}
              placeholder="Describe the bug in detail..."
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={5}
              value={bugDescription}
              onChangeText={setBugDescription}
            />

            <TouchableOpacity
              style={[styles.submitButton, { backgroundColor: colors.accent }]}
              onPress={handleBugReport}
            >
              <Text style={styles.submitButtonText}>Submit Report</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </Modal>

      {/* Delete Account Confirmation Modal */}
      <Modal
        visible={deleteConfirmVisible}
        animationType="fade"
        transparent={true}
        onRequestClose={() => setDeleteConfirmVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.confirmModalContent, { backgroundColor: colors.card }]}>
            <Ionicons name="warning-outline" size={48} color={colors.danger} style={styles.warningIcon} />

            <Text style={[styles.confirmTitle, { color: colors.text }]}>
              Delete Account?
            </Text>

            <Text style={[styles.confirmText, { color: colors.textSecondary }]}>
              This action cannot be undone. All your data, including chat history, will be permanently deleted.
            </Text>

            <View style={styles.confirmButtonsContainer}>
              <TouchableOpacity
                style={[styles.cancelButton, { backgroundColor: colors.surfaceLight }]}
                onPress={() => setDeleteConfirmVisible(false)}
              >
                <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.deleteButton, { backgroundColor: colors.danger }]}
                onPress={handleDeleteAccount}
              >
                <Text style={styles.deleteButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    marginLeft: 8,
    fontSize: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  accountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  accountDetails: {
    marginLeft: 12,
  },
  emailText: {
    fontSize: 16,
    fontWeight: '500',
  },
  nameText: {
    fontSize: 14,
    marginTop: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 1,
  },
  settingText: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  themeIndicator: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  themeIndicatorText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  updateStatusText: {
    fontSize: 12,
    marginLeft: 'auto',
    marginRight: 8,
  },
  appInfo: {
    alignItems: 'center',
    marginVertical: 20,
  },
  appVersion: {
    fontSize: 12,
  },
  disclaimerContent: {
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  disclaimerText: {
    fontSize: 14,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  input: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 16,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmModalContent: {
    margin: 20,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
  },
  warningIcon: {
    marginBottom: 16,
  },
  confirmTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  confirmText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  confirmButtonsContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  deleteButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});
