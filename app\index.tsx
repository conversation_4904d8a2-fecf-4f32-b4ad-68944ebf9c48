import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../src/contexts/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { LampDemo } from '../src/components/ui/LampDemo';
import { Footer } from '../src/components/ui/Footer';
import { useTheme } from '../src/contexts/ThemeContext';

const { height } = Dimensions.get('window');

interface Benefit {
  iconName: keyof typeof Ionicons.glyphMap;
  title: string;
  description: string;
}

export default function HomeScreen() {
  const router = useRouter();
  const { user, isGuest } = useAuth();
  const { colors, isDark } = useTheme();

  const handleStartConversation = () => {
    // If user is not authenticated (not logged in and not a guest), redirect to login
    if (!user && !isGuest) {
      router.push('/auth');
      return;
    }
    // If authenticated, navigate to companions screen
    router.push('/all-companions');
  };

  const logoSource = isDark
    ? require('../assets/icon.png')
    : require('../assets/adaptive-icon.png');

  const themed = {
    borderLight: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
    accentBg: 'rgba(6,182,212,0.1)',
    alertBg: isDark ? 'rgba(239,68,68,0.1)' : 'rgba(255,241,242,0.9)',
    alertBorder: '#FECACA',
    alertTitle: '#991B1B',
    alertText: '#B91C1C',
  };

  const benefits: Benefit[] = [
    {
      iconName: 'time-outline',
      title: 'Always Available',
      description: 'Connect with AI-powered support whenever you need it—day or night.',
    },
    {
      iconName: 'person-outline',
      title: 'Tailored to You',
      description: 'Receive personalized guidance and exercises based on your unique goals.',
    },
    {
      iconName: 'bulb-outline',
      title: 'Evidence-Based Methods',
      description: 'Engage with techniques grounded in clinical research and proven results.',
    },
    {
      iconName: 'shield-checkmark-outline',
      title: 'Secure & Confidential',
      description: 'Your conversations are encrypted and your privacy is our top priority.',
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { borderBottomColor: themed.borderLight }]}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Image source={logoSource} style={styles.headerLogo} resizeMode="contain" />
          <Text style={[styles.headerTitle, { color: colors.text, marginLeft: 8 }]}>Verbalyze</Text>
        </View>
        <TouchableOpacity onPress={() => router.push('/settings')} style={styles.settingsButton}>
          <Ionicons name="settings-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={{ paddingBottom: 0, paddingHorizontal: 0}} showsVerticalScrollIndicator={false}>
        <View style={styles.heroSection}>
          <LampDemo backgroundColor={colors.surface} textColor={colors.text} />
        </View>

        <View style={styles.benefitsOuterContainer}>
          <View style={[styles.benefitsContainer, { backgroundColor: colors.surface }]}>
            {benefits.map((benefit, index) => (
              <View key={index} style={styles.benefitItem}>
                <View style={[styles.benefitIconContainer, { backgroundColor: themed.accentBg }]}>
                  <Ionicons name={benefit.iconName} size={20} color={colors.accent} />
                </View>
                <View style={styles.benefitTextContainer}>
                  <Text style={[styles.benefitTitle, { color: colors.text }]}>{benefit.title}</Text>
                  <Text style={[styles.benefitDescription, { color: colors.text }]} numberOfLines={2}>
                    {benefit.description}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.alertContainer}>
          <View style={[styles.alertBanner, { backgroundColor: themed.alertBg, borderColor: themed.alertBorder }]}>
            <Ionicons name="alert-circle-outline" size={20} color="#DC2626" style={styles.alertIcon} />
            <View style={styles.alertTextContainer}>
              <Text style={[styles.alertTitle, { color: themed.alertTitle }]}>Important Notice</Text>
              <Text style={[styles.alertDescription, { color: themed.alertText }]}>
                This app uses artificial intelligence to generate responses for informational and educational purposes only. It is not intended to provide medical advice, diagnosis, or treatment. The information provided may be inaccurate or incomplete and should not be used as a substitute for professional care. By using this app, you agree that you are solely responsible for your personal decisions. If you are experiencing a crisis or need immediate assistance, please contact a licensed professional, call emergency services, or reach out to your local crisis hotline. Always consult with appropriate professionals for any health-related concerns.
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.ctaContainer}>
          <TouchableOpacity 
            style={[styles.ctaButton, { backgroundColor: colors.accent }]} 
            onPress={handleStartConversation}
            accessibilityLabel="Start a conversation"
            accessibilityHint="Begin chatting with an AI companion. Requires sign in."
          >
            <Text style={[styles.ctaButtonText, { color: 'white' }]}>Start a conversation</Text>
            <Ionicons name="chatbubble-ellipses-outline" size={20} color="white" style={styles.ctaIcon} />
          </TouchableOpacity>
        </View>

        <Footer textColor={colors.text} backgroundColor={colors.surface} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
  },
  headerTitle: { fontSize: 20, fontWeight: 'bold' },
  settingsButton: { padding: 8 },
  scrollView: { flex: 1 },
  scrollContent: { flexGrow: 1, paddingBottom: 20 },
  heroSection: { width: '100%', height: height * 0.52 },
  benefitsOuterContainer: { paddingHorizontal: 16, marginTop: -30 },
  benefitsContainer: { paddingVertical: 16, paddingHorizontal: 16, borderRadius: 12 },
  benefitItem: { flexDirection: 'row', marginBottom: 16, alignItems: 'flex-start' },
  benefitIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  benefitTextContainer: { flex: 1 },
  benefitTitle: { fontSize: 15, fontWeight: '600', marginBottom: 2 },
  benefitDescription: { fontSize: 13, lineHeight: 18 },
  alertContainer: { paddingHorizontal: 16, marginTop: 20 },
  alertBanner: { flexDirection: 'row', borderWidth: 1, borderRadius: 8, padding: 12, alignItems: 'flex-start' },
  alertIcon: { marginRight: 10, marginTop: 2 },
  alertTextContainer: { flex: 1 },
  alertTitle: { fontWeight: '600', fontSize: 16, marginBottom: 10 },
  alertDescription: { fontSize: 15, lineHeight: 20 },
  ctaContainer: { 
    paddingHorizontal: 16, 
    marginTop: 24, 
    marginBottom: 50  // Change from 20 to 0
  },
  ctaButton: {
    flexDirection: 'row',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  ctaButtonText: { fontSize: 16, fontWeight: '600', marginRight: 8 },
  ctaIcon: {},
  headerLogo: {
    width: 24,
    height: 24,
  },
});
