# Message Encryption in Cozy Therapist App

## Overview

- All chat messages stored in the `chat_messages` table are **obfuscated** using a **custom XOR-based algorithm**.
- The encryption key is stored in the `.env` file as:

```
EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY=your-secret-key-here
```

- Messages are **encrypted before saving** to the database and **decrypted after fetching**.
- The encrypted messages are prefixed with `CUSTOM:`.

---

## How to Decrypt Messages in Supabase

### Step 1: Obtain the Encrypted Message

- Query the database directly to get the encrypted message content.
- Example SQL:

```sql
SELECT content FROM chat_messages WHERE session_id = 'your-session-id';
```

### Step 2: Use the `decrypt_message` Function

- Use the `decrypt_message` function in the Supabase SQL editor to decrypt the message.

```sql
SELECT decrypt_message(content) as decrypted_message
FROM chat_messages
WHERE session_id = 'your-session-id';
```

---

## Important Notes

- **Do not share the encryption key publicly.**
- Always handle decrypted data with care.

---

## Contact

For further assistance, contact the development team.